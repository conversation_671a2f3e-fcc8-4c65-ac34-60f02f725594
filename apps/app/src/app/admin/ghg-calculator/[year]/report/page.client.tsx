/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { EmissionsSummary, GHGFormData } from "@/components/ghg-calculator/EmissionsContext"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { ReportSummary } from "@/components/ghg-calculator/ReportSummary"
import { useTranslations } from "next-intl"

import { PathBreadcrumb } from "@kreios/admin-layout/automatic-breadcrumbs"
import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { Button } from "@kreios/ui/button"

interface SavedGHGData extends GHGFormData {
  calculatedTotals?: {
    sectionTotals: Record<string, number>
    scope1Total: number
    scope2Total: number
    scope1AndScope2TotalEmissions: number
    biomass?: number
  }
}

interface YearReportPageProps {
  params: {
    year: string
  }
}

export default function YearReportPage({ params }: YearReportPageProps) {
  const year = parseInt(params.year)
  const t = useTranslations("ghgCalculator.report")
  const router = useRouter()
  const [reportData, setReportData] = useState<SavedGHGData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadReportData = async () => {
      try {
        const savedData = localStorage.getItem(`ghg-calculator-data-${year}`)
        if (savedData) {
          const parsedData = JSON.parse(savedData) as SavedGHGData
          setReportData(parsedData)
        } else {
          setError(t("noDataFound"))
        }
      } catch (error) {
        console.error("Failed to load report data:", error)
        setError(t("loadError"))
      } finally {
        setLoading(false)
      }
    }

    loadReportData()
  }, [year, t])

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mb-4 h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          <p className="text-muted-foreground">{t("loading")}</p>
        </div>
      </div>
    )
  }

  if (error || !reportData) {
    return (
      <>
        <PathBreadcrumb id={params.year}>{year}</PathBreadcrumb>
        <PageHeader
          title={
            <div>
              <h1 className="mb-2 text-3xl font-bold text-foreground">
                {t("title")} - {year}
              </h1>
              <p className="text-muted-foreground">{t("description", { year })}</p>
            </div>
          }
          includeInBreadcrumb={false}
        />
        <div className="mx-auto max-w-4xl px-4 py-8">
          <div className="rounded-lg border border-red-200 bg-red-50 p-6 text-center dark:border-red-800 dark:bg-red-900/20">
            <svg
              className="mx-auto mb-4 h-12 w-12 text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
            <h3 className="mb-2 text-lg font-semibold text-red-800 dark:text-red-200">{t("errorTitle")}</h3>
            <p className="mb-6 text-red-600 dark:text-red-300">{error}</p>
            <div className="flex justify-center gap-4">
              <Button
                onClick={() => router.push("/admin/ghg-calculator")}
                className="bg-gray-600 hover:bg-gray-700 focus:outline-none dark:bg-gray-700 dark:hover:bg-gray-600"
              >
                ← {t("backToOverview")}
              </Button>
              <Button onClick={() => router.push(`/admin/ghg-calculator/${year}`)}>
                {t("calculateEmissionsFor", { year })}
              </Button>
            </div>
          </div>
        </div>
      </>
    )
  }

  // Extract calculated totals from saved data
  const calculatedTotals: EmissionsSummary = reportData.calculatedTotals || {
    sectionTotals: {},
    scope1Total: 0,
    scope2Total: 0,
    scope1AndScope2TotalEmissions: 0,
    biomass: 0,
  }

  return (
    <>
      <PathBreadcrumb id={params.year}>{year}</PathBreadcrumb>
      <PageHeader
        title={
          <div>
            <h1 className="mb-2 text-3xl font-bold text-foreground">
              {t("title")} - {year}
            </h1>
            <p className="text-muted-foreground">{t("description", { year })}</p>
          </div>
        }
        includeInBreadcrumb={false}
      />
      <div className="mx-auto max-w-6xl px-4 py-8">
        <ReportSummary
          year={year}
          calculatedTotals={calculatedTotals}
          lastModified={reportData.metadata?.lastSaved}
        />
        <div className="mt-8 flex justify-center gap-4">
          <Button onClick={() => router.push(`/admin/ghg-calculator/${year}`)}>{t("editData")}</Button>
          <Button
            onClick={() => router.push("/admin/ghg-calculator")}
            className="bg-gray-600 hover:bg-gray-700 focus:outline-none dark:bg-gray-700 dark:hover:bg-gray-600"
          >
            ← {t("backToOverview")}
          </Button>
        </div>
      </div>
    </>
  )
}
