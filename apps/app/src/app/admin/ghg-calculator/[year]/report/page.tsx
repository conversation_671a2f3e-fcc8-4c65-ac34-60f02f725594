/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
import { withConsent } from "@/utils/with-consent"

import YearReportPage from "./page.client"

export default withConsent(YearReportPage)

import { PathBreadcrumb } from "@kreios/admin-layout/automatic-breadcrumbs"
import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { Button } from "@kreios/ui/button"

interface YearReportPageProps {
  params: {
    year: string
  }
}

// Extended type to include saved totals
interface SavedGHGData extends GHGFormData {
  calculatedTotals?: {
    sectionTotals: {
      stationary: number
      mobile: number
      fugitive: number
      agricultural: number
      scope2Electricity: number
      scope2District: number
      biomass?: number
    }
    scope1Total: number
    scope2Total: number
    scope1AndScope2TotalEmissions: number
    biomass?: number
  }
}

export default function YearReportPage({ params }: YearReportPageProps) {
  const year = parseInt(params.year)
  const t = useTranslations("ghgCalculator.report")
  const router = useRouter()
  const [reportData, setReportData] = useState<SavedGHGData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Convert saved data to EmissionsSummary format using actual calculated totals
  const convertToEmissionsSummary = (data: SavedGHGData): EmissionsSummary => {
    const totals = data.calculatedTotals

    // Calculate resPpaAvailable from electricity data
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
    const resPpaAvailable = data.electricity.some((row: any) => row.type === "RES" && row.hasPPA === "YES")

    if (!totals) {
      // Fallback for old data format - show message to user to recalculate
      console.warn("No calculated totals found. Please save the data again to see accurate totals.")
      return {
        scope1: { total: 0, stationary: 0, mobile: 0, fugitive: 0, agricultural: 0, biogenic: 0 },
        scope2: { total: 0, electricity: 0, district: 0 },
        resPpaAvailable,
        biomass: 0,
      }
    }

    return {
      scope1: {
        total: totals.scope1Total || 0,
        stationary: totals.sectionTotals.stationary || 0,
        mobile: totals.sectionTotals.mobile || 0,
        fugitive: totals.sectionTotals.fugitive || 0,
        agricultural: totals.sectionTotals.agricultural || 0,
        biogenic: 0, // Would need to be calculated separately for biomass CO2
      },
      scope2: {
        total: totals.scope2Total || 0,
        electricity: totals.sectionTotals.scope2Electricity || 0,
        district: totals.sectionTotals.scope2District || 0,
      },
      resPpaAvailable,
      biomass: totals.biomass ?? totals.sectionTotals.biomass ?? 0,
    }
  }

  useEffect(() => {
    const loadReportData = () => {
      try {
        const savedData = localStorage.getItem(`ghg-calculator-data-${year}`)
        if (savedData) {
          const parsedData = JSON.parse(savedData) as SavedGHGData
          setReportData(parsedData)
        } else {
          setError(`No emissions data found for year ${year}. Please calculate emissions first.`)
        }
      } catch (error) {
        console.error(`Failed to load report data for year ${year}:`, error)
        setError(`Failed to load report data for year ${year}. Please try again.`)
      } finally {
        setLoading(false)
      }
    }

    loadReportData()
  }, [year])

  if (loading) {
    return (
      <div className="p-2 sm:p-4 md:p-8">
        <PathBreadcrumb id={params.year}>{year}</PathBreadcrumb>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 animate-spin rounded-full border-b-2 border-primary"></div>
            <p className="mt-4 text-muted-foreground">Loading report data...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-2 sm:p-4 md:p-8">
        <PathBreadcrumb id={params.year}>{year}</PathBreadcrumb>
        <div className="mb-8 flex items-center justify-between gap-2">
          <div>
            <h1 className="mb-2 text-3xl font-bold text-foreground">{t("reportTitle", { year })}</h1>
          </div>
          <Button
            onClick={() => router.push("/admin/ghg-calculator")}
            className="bg-gray-600 hover:bg-gray-700 focus:outline-none dark:bg-gray-700 dark:hover:bg-gray-600"
          >
            ← {t("backToOverview")}
          </Button>
        </div>

        <div className="rounded-lg border border-red-700 bg-red-100 p-6 text-center text-red-700 dark:border-red-300 dark:bg-red-900 dark:text-red-300">
          <svg
            className="mx-auto h-12 w-12 text-red-400 dark:text-red-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
          <h3 className="mt-2 text-lg font-medium text-red-900 dark:text-red-100">{t("noDataTitle")}</h3>
          <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
          <div className="mt-6">
            <Button onClick={() => router.push(`/admin/ghg-calculator/${year}`)}>
              {t("calculateEmissionsFor", { year })}
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (!reportData) {
    return null
  }

  return (
    <div>
      <PathBreadcrumb id={params.year}>{year}</PathBreadcrumb>
      <PageHeader
        title={
          <div>
            <h1 className="mb-2 text-3xl font-bold text-foreground">{t("reportTitle", { year })}</h1>
            <p className="text-muted-foreground">{t("reportDescription", { year })}</p>
          </div>
        }
        includeInBreadcrumb={false}
      >
        <div className="flex gap-4">
          <Button onClick={() => router.push(`/admin/ghg-calculator/${year}`)}>{t("editData")}</Button>
          <Button
            onClick={() => router.push("/admin/ghg-calculator")}
            className="bg-gray-600 hover:bg-gray-700 focus:outline-none dark:bg-gray-700 dark:hover:bg-gray-600"
          >
            ← {t("backToOverview")}
          </Button>
        </div>
      </PageHeader>

      {/* Report Summary Component */}
      <ReportSummary emissions={convertToEmissionsSummary(reportData)} />
    </div>
  )
}
