/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { SectionKey } from "@/components/ghg-calculator/calculatorSections"
import type { GHGFormData } from "@/components/ghg-calculator/EmissionsContext"
import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { scope1Sections, scope2Sections } from "@/components/ghg-calculator/calculatorSections"
import { useEmissions } from "@/components/ghg-calculator/EmissionsContext"
import SectionCard from "@/components/ghg-calculator/SectionCard"
import { formatNumber } from "@/utils/formatNumber"
import { useTranslations } from "next-intl"

import { PathBreadcrumb } from "@kreios/admin-layout/automatic-breadcrumbs"
import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { Button } from "@kreios/ui/button"

interface YearCalculatorPageProps {
  params: {
    year: string
  }
}

export default function YearCalculatorPage({ params }: YearCalculatorPageProps) {
  const year = parseInt(params.year)
  const t = useTranslations("ghgCalculator")
  const { formData, setFormData, resetFormData, emissions } = useEmissions()
  const router = useRouter()
  const [sectionTotals, setSectionTotals] = useState<Record<SectionKey, number>>({
    stationary: 0,
    mobile: 0,
    fugitive: 0,
    agricultural: 0,
    scope2Electricity: 0,
    scope2District: 0,
  })
  const [isSaving, setIsSaving] = useState(false)
  // TODO: will use save message later
  const [_saveMessage, setSaveMessage] = useState<string>("")

  const scope1Total = sectionTotals.stationary + sectionTotals.mobile + sectionTotals.fugitive
  const scope2Total = sectionTotals.scope2Electricity + sectionTotals.scope2District

  // Load saved data for this specific year (only run when year changes)
  useEffect(() => {
    try {
      const savedData = localStorage.getItem(`ghg-calculator-data-${year}`)
      if (savedData) {
        const parsedData = JSON.parse(savedData) as GHGFormData
        setFormData(parsedData)
        console.log(`Loaded saved GHG Calculator data for year ${year}`, parsedData)
      } else {
        // Reset to default if no data for this year
        console.log(`No saved data for year ${year}, resetting to default`)
        resetFormData()
      }
    } catch (error) {
      console.error(`Failed to load saved data for year ${year}:`, error)
      resetFormData()
    }
  }, [year]) // Only depend on year, not on setFormData or resetFormData

  const handleSave = async () => {
    setIsSaving(true)
    setSaveMessage("")
    try {
      const dataToSave = {
        ...formData,
        metadata: {
          ...formData.metadata,
          lastSaved: new Date().toISOString(),
          year: year,
        },
        // Save the calculated totals
        calculatedTotals: {
          sectionTotals: {
            ...sectionTotals,
            biomass: emissions.biomass ?? 0,
          },
          scope1Total: scope1Total,
          scope2Total: scope2Total,
          scope1AndScope2TotalEmissions: scope1Total + scope2Total,
          biomass: emissions.biomass ?? 0,
        },
      }

      localStorage.setItem(`ghg-calculator-data-${year}`, JSON.stringify(dataToSave))
      setSaveMessage(t("saveSuccess"))
      setTimeout(() => setSaveMessage(""), 3000)
    } catch (error) {
      setSaveMessage(t("saveError"))
      console.error(error)
      setTimeout(() => setSaveMessage(""), 3000)
    } finally {
      setIsSaving(false)
    }
  }

  const handleReset = () => {
    if (confirm(t("resetConfirm"))) {
      resetFormData()
      localStorage.removeItem(`ghg-calculator-data-${year}`)
      setSaveMessage(t("resetSuccess"))
      setTimeout(() => setSaveMessage(""), 3000)
    }
  }

  const handleTotalChange = (section: SectionKey, total: number) => {
    setSectionTotals((prev) => ({ ...prev, [section]: total }))
  }

  return (
    <>
      <PathBreadcrumb id={params.year}>{year}</PathBreadcrumb>
      <PageHeader
        title={
          <div>
            <h1 className="mb-2 text-3xl font-bold text-foreground">
              {t("title")} - {year}
            </h1>
            <p className="text-muted-foreground">{t("description", { year })}</p>
          </div>
        }
        includeInBreadcrumb={false}
      />
      <div className="sticky top-20 z-20 ml-auto mt-4 flex gap-3 px-2 sm:px-4 md:px-8">
        {" "}
        <Button type="button" onClick={handleSave} disabled={isSaving}>
          {isSaving ? t("saving") : t("save")}
        </Button>
        <Button variant={"destructive"} onClick={handleReset}>
          {t("resetAll")}
        </Button>
        <Button
          onClick={() => {
            router.push(`/admin/ghg-calculator/${year}/report`)
          }}
          className="inline-flex items-center gap-2 rounded bg-gray-600 px-6 py-2 text-white hover:bg-gray-700 focus:outline-none dark:bg-gray-700 dark:hover:bg-gray-600"
        >
          {t("generateReport")}
        </Button>
        {/* <Button onClick={() => (window.location.href = "/admin/ghg-calculator")}>← Back to Overview</Button> */}
      </div>
      <div className="mx-2 mt-6 sm:mx-6 md:mx-8">
        {/* Summary Cards */}
        <div className="mb-8 grid w-full grid-cols-1 gap-4 md:grid-cols-3">
          <div className="rounded-lg border border-green-600 bg-green-100 p-6 text-green-700 dark:border-green-300 dark:bg-green-900 dark:text-green-300">
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-100">{t("scope1Emissions")}</h3>
            <p className="text-2xl font-bold text-green-600 dark:text-green-400">{formatNumber(scope1Total)} tCO₂e</p>
            <p className="text-sm text-green-700 dark:text-green-300">{t("scope1Description")}</p>
          </div>
          <div className="rounded-lg border border-green-700 bg-green-200 p-6 text-green-800 dark:border-green-200 dark:bg-green-800 dark:text-green-200">
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-100">{t("scope2Emissions")}</h3>
            <p className="text-2xl font-bold text-green-700 dark:text-green-400">{formatNumber(scope2Total)} tCO₂e</p>
            <p className="text-sm text-green-800 dark:text-green-300">{t("scope2Description")}</p>
          </div>
          <div className="rounded-lg border border-green-800 bg-green-300 p-6 text-green-900 dark:border-green-100 dark:bg-green-700 dark:text-green-100">
            <h3 className="text-lg font-semibold text-green-900 dark:text-green-100">{t("totalEmissions")}</h3>
            <p className="text-2xl font-bold text-green-800 dark:text-green-200">
              {formatNumber(scope1Total + scope2Total)} tCO₂e
            </p>
            <p className="text-sm text-green-900 dark:text-green-200">{t("totalDescription")}</p>
          </div>
        </div>

        {/* Scope 1 Sections */}
        <div className="mb-8 w-full">
          <h2 className="mb-4 text-2xl font-semibold text-foreground">{t("sectionTitles.scope1")}</h2>
          <div className="w-full space-y-6">
            {scope1Sections.map((section) => (
              <SectionCard
                key={section.key}
                icon={section.icon}
                title={t(`sections.${section.key}`)}
                total={sectionTotals[section.key]}
              >
                <section.Form onTotalChange={(total) => handleTotalChange(section.key, total)} />
              </SectionCard>
            ))}
          </div>
        </div>

        {/* Scope 2 Sections */}
        <div className="mb-8 w-full">
          <h2 className="mb-4 text-2xl font-semibold text-foreground">{t("sectionTitles.scope2")}</h2>
          <div className="w-full space-y-6">
            {scope2Sections.map((section) => (
              <SectionCard
                key={section.key}
                icon={section.icon}
                title={t(`sections.${section.key}`)}
                total={sectionTotals[section.key]}
              >
                <section.Form onTotalChange={(total) => handleTotalChange(section.key, total)} />
              </SectionCard>
            ))}
          </div>
        </div>
      </div>
    </>
  )
}
