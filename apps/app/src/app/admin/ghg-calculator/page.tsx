/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { GHGFormData } from "@/components/ghg-calculator/EmissionsContext"
import { useEffect, useState } from "react"
import { formatNumber } from "@/utils/formatNumber"
import { Edit, FileText } from "lucide-react"
import { useTranslations } from "next-intl"

import { PageHeader } from "@kreios/admin-layout/components/page-header"
import { Button } from "@kreios/ui/button"
import { Card, CardHeader } from "@kreios/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@kreios/ui/dialog"
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@kreios/ui/select"

interface YearlyEmissionData {
  year: number
  scope1Total: number
  scope2Total: number
  scope1AndScope2TotalEmissions: number
  lastModified: string
  hasData: boolean
}

interface SavedGHGDataWithTotals extends GHGFormData {
  calculatedTotals?: {
    sectionTotals: Record<string, number>
    scope1Total: number
    scope2Total: number
    scope1AndScope2TotalEmissions: number
  }
}

export default function GHGCalculatorMainPage() {
  const t = useTranslations("ghgCalculator")
  const [yearlyData, setYearlyData] = useState<YearlyEmissionData[]>([])
  const [showYearModal, setShowYearModal] = useState(false)
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear() - 1)

  // Load all yearly data from localStorage
  useEffect(() => {
    const loadYearlyData = () => {
      const data: YearlyEmissionData[] = []
      const currentYear = new Date().getFullYear()

      // Check last 5 years for existing data
      for (let year = currentYear; year >= currentYear - 4; year--) {
        const savedData = localStorage.getItem(`ghg-calculator-data-${year}`)
        if (savedData) {
          try {
            const parsedData = JSON.parse(savedData) as SavedGHGDataWithTotals

            // Use actual saved calculated totals (with fallback to 0 if not available)
            const scope1Total: number = parsedData.calculatedTotals?.scope1Total ?? 0
            const scope2Total: number = parsedData.calculatedTotals?.scope2Total ?? 0

            data.push({
              year,
              scope1Total,
              scope2Total,
              scope1AndScope2TotalEmissions: scope1Total + scope2Total,
              lastModified: parsedData.metadata?.lastSaved ?? "Unknown",
              hasData: true,
            })
          } catch (error) {
            console.error(`Failed to parse data for year ${year}:`, error)
          }
        }
      }

      setYearlyData(data)
    }

    loadYearlyData()
  }, [])

  const handleCalculateEmissions = () => {
    setShowYearModal(true)
  }

  const handleYearSelect = () => {
    setShowYearModal(false)
    // Navigate to year-specific calculator
    window.location.href = `/admin/ghg-calculator/${selectedYear}`
  }

  const handleViewReport = (year: number) => {
    window.location.href = `/admin/ghg-calculator/${year}/report`
  }

  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear()
    const years = []
    for (let year = currentYear; year >= currentYear - 4; year--) {
      years.push(year)
    }
    return years
  }

  return (
    <div>
      <PageHeader
        title={
          <div>
            <h1 className="mb-2 text-3xl font-bold">{t("mainTitle")}</h1>
            <p className="text-muted-foreground">{t("mainDescription")}</p>
          </div>
        }
        includeInBreadcrumb={false}
      >
        <Button onClick={handleCalculateEmissions}>
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          {t("calculateEmissions")}
        </Button>
      </PageHeader>
      {/* Emissions Data Table */}
      <Card className="m-2 flex flex-col sm:m-4 md:m-8">
        <CardHeader className="flex flex-row justify-between gap-2 pb-4">
          <h2 className="text-lg font-semibold">{t("emissionsHistory")}</h2>
        </CardHeader>

        {yearlyData.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border">
              <thead className="bg-muted">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                    {t("year")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                    {t("scope1EmissionsTable")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                    {t("scope2EmissionsTable")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                    {t("totalEmissionsTable")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                    {t("lastModified")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-muted-foreground">
                    {t("actions")}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-border bg-background">
                {yearlyData.map((data) => (
                  <tr key={data.year} className="hover:bg-muted/50">
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-foreground">{data.year}</td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-foreground">
                      {formatNumber(data.scope1Total)} tCO₂e
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-foreground">
                      {formatNumber(data.scope2Total)} tCO₂e
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-foreground">
                      {formatNumber(data.scope1AndScope2TotalEmissions)} tCO₂e
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-sm text-muted-foreground">
                      {new Date(data.lastModified).toLocaleDateString()}
                    </td>
                    <td className="space-x-4 whitespace-nowrap px-6 py-4 text-sm font-medium">
                      <button
                        onClick={() => (window.location.href = `/admin/ghg-calculator/${data.year}`)}
                        className="text-primary hover:text-primary/80"
                        title={t("edit")}
                      >
                        <Edit className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleViewReport(data.year)}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        title={t("viewReport")}
                      >
                        <FileText className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="px-6 py-12 text-center">
            <svg
              className="mx-auto h-12 w-12 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-foreground">{t("noEmissionsData")}</h3>
            <p className="mt-1 text-sm text-muted-foreground">{t("noEmissionsDescription")}</p>
            <div className="mt-6">
              <Button onClick={handleCalculateEmissions}>{t("calculateEmissions")}</Button>
            </div>
          </div>
        )}
      </Card>

      {/* Year Selection Modal */}
      {showYearModal && (
        <Dialog open={showYearModal} onOpenChange={() => setShowYearModal(false)}>
          <DialogContent className="max-w-[] p-4 lg:p-6" aria-describedby={undefined}>
            <DialogHeader>
              <DialogTitle className="text-xl font-semibold lg:text-2xl">{t("selectYear")}</DialogTitle>
              <p className="text-sm text-gray-600 dark:text-gray-300 lg:text-base">{t("selectYearDescription")}</p>
            </DialogHeader>

            <div className="mt-4">
              <label htmlFor="year-select" className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-200">
                {t("selectYearLabel")}
              </label>
              <Select value={String(selectedYear)} onValueChange={(value) => setSelectedYear(parseInt(value))}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={t("selectYearPlaceholder")} />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {generateYearOptions().map((year) => (
                      <SelectItem key={year} value={String(year)}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>

            <div className="mt-6 flex justify-end gap-4">
              <Button onClick={handleYearSelect}>{t("continue")}</Button>
              <Button variant="outline" onClick={() => setShowYearModal(false)}>
                {t("cancel")}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
