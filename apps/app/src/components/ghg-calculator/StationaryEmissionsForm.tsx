/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type { StationaryRow } from "@/lib/ghg-calculator/types"
import { useEffect, useState } from "react"
import { useEmissions } from "@/components/ghg-calculator/EmissionsContext"
import { calculateStationaryEmissions, estimateEmissionsByArea } from "@/lib/ghg-calculator/calc-logic"
import { formSchema } from "@/lib/ghg-calculator/formSchema"
import { formatNumber } from "@/utils/formatNumber"
import { useTranslations } from "next-intl"

import { Button } from "@kreios/ui/button"

import type { Column } from "./DynamicTable"
import { DynamicTable } from "./DynamicTable"

interface StationaryEmissionsFormProps {
  onTotalChange?: (total: number) => void
}

const stationarySchema = formSchema.sections.find((s) => s.id === "scope1_stationary")

if (!stationarySchema) throw new Error("Missing schema for stationary emissions")

const columns = stationarySchema.columns

const getDefaultRow = (category: "fossil" | "biomass"): StationaryRow => {
  const row: Record<string, string> = {}
  for (const col of columns) {
    if (col.type === "select") {
      if (col.id === "fuel") {
        // For fuel, filter by category
        const opts = (col.options as { value: string; category: string }[]).filter((opt) => opt.category === category)
        row[col.id] = opts[0]?.value || ""
      } else if (col.id === "unit") {
        // Set default unit based on category
        if (category === "biomass") {
          row[col.id] = "M3" // Default to M3 for biomass
        } else {
          row[col.id] = (col.options as string[])[0] || "" // First option for fossil fuels
        }
      } else {
        row[col.id] = (col.options as string[])[0] || ""
      }
    } else {
      row[col.id] = ""
    }
  }
  return row as StationaryRow
}

export default function StationaryEmissionsForm({ onTotalChange }: StationaryEmissionsFormProps) {
  const t = useTranslations("ghgCalculator.forms.stationary")
  const tCommon = useTranslations("ghgCalculator.forms.common")
  const { setEmissions, formData, setFormData } = useEmissions()
  // Separate state for fuel and biomass rows - initialize from context
  const [fuelRows, setFuelRows] = useState<StationaryRow[]>(formData.stationary.fuelRows)
  const [biomassRows, setBiomassRows] = useState<StationaryRow[]>(formData.stationary.biomassRows)

  // Sync local state with context when formData changes
  useEffect(() => {
    setFuelRows(formData.stationary.fuelRows)
    setBiomassRows(formData.stationary.biomassRows)
  }, [formData.stationary.fuelRows, formData.stationary.biomassRows])

  // Add row handlers
  const addFuelRow = () => {
    const newRows = [...fuelRows, getDefaultRow("fossil")]
    setFuelRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      stationary: {
        ...prev.stationary,
        fuelRows: newRows,
      },
    }))
  }
  const addBiomassRow = () => {
    const newRow = getDefaultRow("biomass")
    console.log("Adding biomass row with default values:", newRow)
    const newRows = [...biomassRows, newRow]
    setBiomassRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      stationary: {
        ...prev.stationary,
        biomassRows: newRows,
      },
    }))
  }
  // Delete row handlers
  const deleteFuelRow = (index: number) => {
    const newRows = fuelRows.filter((_, i) => i !== index)
    setFuelRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      stationary: {
        ...prev.stationary,
        fuelRows: newRows,
      },
    }))
  }
  const deleteBiomassRow = (index: number) => {
    const newRows = biomassRows.filter((_, i) => i !== index)
    setBiomassRows(newRows)
    // Update context
    setFormData((prev) => ({
      ...prev,
      stationary: {
        ...prev.stationary,
        biomassRows: newRows,
      },
    }))
  }
  // Update row handlers
  const updateFuelRow = (index: number, key: keyof StationaryRow, value: string) => {
    const updated = [...fuelRows]
    if (key === "consumption" || key === "unit") {
      updated[index].area = ""
      updated[index].areaUnit = ""
    }
    if (key === "area" || key === "areaUnit") {
      updated[index].consumption = ""
      updated[index].unit = ""
    }
    updated[index][key] = value
    setFuelRows(updated)
    // Update context
    setFormData((prev) => ({
      ...prev,
      stationary: {
        ...prev.stationary,
        fuelRows: updated,
      },
    }))
  }
  const updateBiomassRow = (index: number, key: keyof StationaryRow, value: string) => {
    const updated = [...biomassRows]
    if (key === "consumption" || key === "unit") {
      updated[index].area = ""
      updated[index].areaUnit = ""
    }
    if (key === "area" || key === "areaUnit") {
      updated[index].consumption = ""
      updated[index].unit = ""
    }
    updated[index][key] = value
    setBiomassRows(updated)
    // Update context
    setFormData((prev) => ({
      ...prev,
      stationary: {
        ...prev.stationary,
        biomassRows: updated,
      },
    }))
  }
  // Totals
  const fuelTotal = fuelRows.reduce((sum, row) => {
    if (row.fuel) {
      if (row.consumption && row.unit) {
        const val = calculateStationaryEmissions(row.fuel, parseFloat(row.consumption), row.unit)
        if (!isNaN(val)) return sum + val
      } else if (row.area && row.areaUnit) {
        const val = estimateEmissionsByArea(row.fuel, parseFloat(row.area), row.areaUnit)
        if (!isNaN(val)) return sum + val
      }
    }
    return sum
  }, 0)
  const biomassTotal = biomassRows.reduce((sum, row) => {
    if (row.fuel) {
      if (row.consumption && row.unit) {
        const val = calculateStationaryEmissions(row.fuel, parseFloat(row.consumption), row.unit)
        if (!isNaN(val)) return sum + val
      } else if (row.area && row.areaUnit) {
        const val = estimateEmissionsByArea(row.fuel, parseFloat(row.area), row.areaUnit)
        if (!isNaN(val)) return sum + val
      }
    }
    return sum
  }, 0)
  useEffect(() => {
    if (onTotalChange) onTotalChange(fuelTotal)
    setEmissions((prev) => ({
      ...prev,
      scope1: {
        ...prev.scope1,
        stationary: fuelTotal,
        total: fuelTotal + prev.scope1.mobile + prev.scope1.fugitive + prev.scope1.agricultural + prev.scope1.biogenic,
      },
      biomass: biomassTotal,
    }))
  }, [fuelTotal, biomassTotal])

  // Helper for emissions column
  const renderEmissions = (row: StationaryRow) => {
    if (row.fuel && row.consumption && row.unit) {
      return formatNumber(calculateStationaryEmissions(row.fuel, parseFloat(row.consumption), row.unit))
    } else if (row.fuel && row.area && row.areaUnit) {
      return formatNumber(estimateEmissionsByArea(row.fuel, parseFloat(row.area), row.areaUnit))
    }
    return "-"
  }

  return (
    <>
      <div className="mb-2 text-sm text-gray-600 dark:text-gray-400">{t("description")}</div>
      {/* Fuels Table */}
      <p className="mb-2 rounded border border-green-600 bg-background px-3 py-2 text-sm font-medium text-foreground">
        {t("annualConsumptionNote")}
      </p>
      <h3 className="mb-2 mt-4 text-lg font-semibold">{t("fuelsTitle")}</h3>
      <div className="mb-8 overflow-x-auto">
        <DynamicTable<StationaryRow>
          columns={columns as Column[]}
          rows={fuelRows}
          onRowChange={(rowIndex, key, value) => updateFuelRow(rowIndex, key, value as string)}
          onRowDelete={deleteFuelRow}
          getSelectOptions={(col) => {
            if (col.id === "fuel") {
              return (columns.find((c) => c.id === "fuel")!.options as { value: string; category: string }[])
                .filter((opt) => opt.category === "fossil")
                .map((opt) => opt.value)
            }
            if (col.id === "unit") {
              // Fossil fuels use all units
              return ["KWH", "LITRES", "M3", "TONNES"]
            }
            if (col.type === "select") {
              return col.options!
            }
            return []
          }}
          extraColumns={[
            (row: StationaryRow) => <span className="text-right font-semibold">{renderEmissions(row)}</span>,
          ]}
          extraColumnLabels={[tCommon("total", { total: "" }).replace(": ", " (tCO₂e)")]}
          renderCell={(col, row) => {
            const hasConsumptionAndUnit = !!row.consumption && !!row.unit
            const hasAreaAndAreaUnit = !!row.area && !!row.areaUnit

            // If area/areaUnit are filled, consumption/unit should be N/A
            if ((col.id === "consumption" || col.id === "unit") && hasAreaAndAreaUnit) {
              return <span className="text-gray-400"></span>
            }
            // If consumption/unit are filled, area/areaUnit should be N/A
            if ((col.id === "area" || col.id === "areaUnit") && hasConsumptionAndUnit) {
              return <span className="text-gray-400"></span>
            }
            // Otherwise, editable
            return undefined
          }}
        />
        <div className="my-4 flex items-center justify-between">
          <span className="text-base font-bold text-primary">
            {tCommon("total", { total: formatNumber(fuelTotal) })}
          </span>
          <Button onClick={addFuelRow}>{tCommon("addRow")}</Button>
        </div>
      </div>
      {/* Biomass Table */}
      <h3 className="mb-2 mt-8 text-lg font-semibold">{t("biomassTitle")}</h3>
      <div className="mb-4 overflow-x-auto">
        <DynamicTable<StationaryRow>
          columns={columns as Column[]}
          rows={biomassRows}
          onRowChange={(rowIndex, key, value) => updateBiomassRow(rowIndex, key, value as string)}
          onRowDelete={deleteBiomassRow}
          getSelectOptions={(col) => {
            if (col.id === "fuel") {
              return (columns.find((c) => c.id === "fuel")!.options as { value: string; category: string }[])
                .filter((opt) => opt.category === "biomass")
                .map((opt) => opt.value)
            }
            if (col.id === "unit") {
              // Biomass only uses M3 and TONNES
              return ["M3", "TONNES"]
            }
            if (col.type === "select") {
              return col.options!
            }
            return []
          }}
          extraColumns={[
            (row: StationaryRow) => <span className="text-right font-semibold">{renderEmissions(row)}</span>,
          ]}
          extraColumnLabels={[tCommon("total", { total: "" }).replace(": ", " (tCO₂e)")]}
          renderCell={(col, row) => {
            const hasConsumptionAndUnit = !!row.consumption && !!row.unit
            const hasAreaAndAreaUnit = !!row.area && !!row.areaUnit
            if ((col.id === "consumption" || col.id === "unit") && hasAreaAndAreaUnit) {
              return <span className="text-gray-400"></span>
            }
            if ((col.id === "area" || col.id === "areaUnit") && hasConsumptionAndUnit) {
              return <span className="text-gray-400"></span>
            }
            return undefined
          }}
        />
        <div className="my-4 flex items-center justify-between">
          <span className="text-base font-bold text-primary">
            {tCommon("total", { total: formatNumber(biomassTotal) })}
          </span>
          <Button onClick={addBiomassRow}>{tCommon("addRow")}</Button>
        </div>
      </div>
    </>
  )
}
