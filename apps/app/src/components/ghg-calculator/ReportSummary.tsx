/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */
"use client"

import React from "react"
import { useTranslations } from "next-intl"
import { Bar, BarChart, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"

import { Card, CardContent, CardHeader, CardTitle } from "@kreios/ui/card"
import { ChartContainer, ChartLegend, ChartLegendContent } from "@kreios/ui/chart"

import { ReportBreakdownTable } from "./ReportBreakdownTable"
import { ReportComparisons } from "./ReportComparisons"
import { ReportSummaryCards } from "./ReportSummaryCards"

// Example props type (replace with your actual emissions summary type)
export type EmissionsSummary = {
  scope1: {
    total: number
    stationary: number
    mobile: number
    fugitive: number
    agricultural: number
    biogenic: number
  }
  scope2: {
    total: number
    electricity: number
    district: number
  }
  resPpaAvailable?: boolean
  biomass?: number
}

type ReportSummaryProps = {
  emissions: EmissionsSummary
}

export const ReportSummary: React.FC<ReportSummaryProps> = ({ emissions }) => {
  const t = useTranslations("ghgCalculator.report")
  const scope1AndScope2TotalEmissions = emissions.scope1.total + emissions.scope2.total

  // Comparison calculations
  const trees = Math.round(scope1AndScope2TotalEmissions / 21)
  const kms = Math.round(scope1AndScope2TotalEmissions * 4000)
  const globes = ((scope1AndScope2TotalEmissions * 4000) / 40075).toFixed(2)

  // Number formatter for thousands separator and two decimals
  const formatNumber = (value: number) =>
    new Intl.NumberFormat("en-US", { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(value)

  // Chart data for Scope 1 and 2 share
  const scopeShareData = [
    { name: "Scope 1", value: emissions.scope1.total, fill: "#16a34a" }, // green-600
    { name: "Scope 2", value: emissions.scope2.total, fill: "#15803d" }, // green-700
  ]
  const scopeShareConfig = {
    value: { label: "Value" },
    "Scope 1": { label: "Scope 1", color: "#16a34a" },
    "Scope 2": { label: "Scope 2", color: "#15803d" },
  }

  // Chart data for Scope 1 breakdown
  const scope1BreakdownData = [
    { name: t("stationaryEmissions"), value: emissions.scope1.stationary },
    { name: t("nonStationaryEmissions"), value: emissions.scope1.mobile },
    { name: t("fugitiveEmissions"), value: emissions.scope1.fugitive },
    { name: t("agricultural"), value: emissions.scope1.agricultural },
    { name: t("biogenicEmissions"), value: emissions.scope1.biogenic },
  ]

  // Chart data for Scope 2 breakdown
  const scope2BreakdownData = [
    { name: t("electricity"), value: emissions.scope2.electricity },
    { name: t("districtEmissions"), value: emissions.scope2.district },
  ]

  // Calculations

  return (
    <>
      <style>{`
        .recharts-bar-rectangle:hover {
          fill: #22c55e !important;
        }
        .recharts-active-bar {
          fill: #22c55e !important;
        }
        .recharts-sector:hover {
          filter: brightness(1.1) !important;
        }
        .recharts-surface {
          background: hsl(var(--muted) / 0.8) !important;
        }
        .recharts-cartesian-grid-horizontal line,
        .recharts-cartesian-grid-vertical line {
          stroke: hsl(var(--muted-foreground)) !important;
          opacity: 0.15;
        }
        .recharts-wrapper {
          background: hsl(var(--muted) / 0.6) !important;
          border-radius: 8px;
        }
        .recharts-default-tooltip {
          background: hsl(var(--background)) !important;
          border: 1px solid hsl(var(--border)) !important;
          border-radius: 6px !important;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
        }
      `}</style>
      <div className="p-2 sm:p-4 md:p-8">
        <ReportSummaryCards
          total={scope1AndScope2TotalEmissions}
          scope1={emissions.scope1.total}
          scope2={emissions.scope2.total}
        />
        <ReportComparisons trees={trees} kms={kms} globes={globes} />
        <ReportBreakdownTable emissions={emissions} resPpaAvailable={emissions.resPpaAvailable ?? false} />
        <hr className="my-8 border-0 border-t-4 border-border" />
        {/* Emissions not included in Scopes 1 and 2 */}
        <div className="my-8 box-border flex w-full flex-col items-start rounded-lg border border-border bg-muted p-4">
          <div className="mb-2 text-[16px] font-bold tracking-[0.1px] text-foreground">{t("emissionsNotIncluded")}</div>
          <div className="mb-1 flex w-full flex-wrap items-center gap-5">
            <span className="min-w-[200px] text-[15px] text-foreground">{t("emissionsFromBiomass")}</span>
            <span className="min-w-[60px] rounded bg-background px-4 py-1 text-center text-[20px] font-bold tracking-[0.2px] text-foreground">
              {formatNumber(emissions.biomass ?? 0)}
            </span>
            <span className="ml-2 text-[15px] font-medium text-muted-foreground">{t("tco2ePerYear")}</span>
          </div>
          <div className="ml-1 mt-0.5 max-w-[900px] text-[13px] italic text-muted-foreground">{t("biomassNote")}</div>
        </div>

        {/* Charts Section - moved above GHG Emissions Report */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>{t("shareOfScope1And2")}</CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer config={scopeShareConfig} className="aspect-auto h-[250px]">
              <PieChart width={300} height={250}>
                <Pie
                  data={scopeShareData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  label={({ value }) => formatNumber(Number(value))}
                  stroke="hsl(var(--background))"
                  strokeWidth={2}
                />
                <Tooltip
                  formatter={(value) => [formatNumber(Number(value)), "tCO₂e"]}
                  contentStyle={{
                    backgroundColor: "hsl(var(--background))",
                    border: "1px solid hsl(var(--border))",
                    borderRadius: "6px",
                    color: "hsl(var(--foreground))",
                  }}
                  labelStyle={{ color: "hsl(var(--foreground))" }}
                />
                <ChartLegend content={<ChartLegendContent nameKey="name" />} className="flex-wrap gap-2" />
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>{t("breakdownOfScope1")}</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={scope1BreakdownData} margin={{ top: 40, right: 30, left: 20, bottom: 60 }}>
                <XAxis
                  dataKey="name"
                  tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  axisLine={{ stroke: "hsl(var(--border))" }}
                  tickLine={{ stroke: "hsl(var(--border))" }}
                />
                <YAxis
                  tickFormatter={formatNumber}
                  tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                  axisLine={{ stroke: "hsl(var(--border))" }}
                  tickLine={{ stroke: "hsl(var(--border))" }}
                />
                <Tooltip
                  formatter={(value) => [formatNumber(Number(value)), "tCO₂e"]}
                  contentStyle={{
                    backgroundColor: "hsl(var(--background))",
                    border: "1px solid hsl(var(--border))",
                    borderRadius: "6px",
                    color: "hsl(var(--foreground))",
                  }}
                  labelStyle={{ color: "hsl(var(--foreground))" }}
                />
                <Bar
                  dataKey="value"
                  fill="#16a34a"
                  radius={[4, 4, 0, 0]}
                  maxBarSize={80}
                  label={{ position: "top", formatter: (value: number) => formatNumber(value) }}
                  stroke="hsl(var(--border))"
                  strokeWidth={1}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>{t("breakdownOfScope2")}</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={scope2BreakdownData} margin={{ top: 40, right: 30, left: 20, bottom: 60 }}>
                <XAxis
                  dataKey="name"
                  tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }}
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis tickFormatter={formatNumber} tick={{ fontSize: 12, fill: "hsl(var(--foreground))" }} />
                <Tooltip
                  formatter={(value) => [formatNumber(Number(value)), "tCO₂e"]}
                  contentStyle={{
                    backgroundColor: "hsl(var(--background))",
                    border: "1px solid hsl(var(--border))",
                    borderRadius: "6px",
                    color: "hsl(var(--foreground))",
                  }}
                  labelStyle={{ color: "hsl(var(--foreground))" }}
                />
                <Bar
                  dataKey="value"
                  fill="#15803d"
                  radius={[4, 4, 0, 0]}
                  maxBarSize={80}
                  label={{ position: "top", formatter: (value: number) => formatNumber(value) }}
                  stroke="hsl(var(--border))"
                  strokeWidth={1}
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </>
  )
}
