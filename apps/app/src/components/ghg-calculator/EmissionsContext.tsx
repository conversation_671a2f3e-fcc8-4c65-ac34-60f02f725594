/**
 * This file is part of the IMPACTLY CLIENT PORTAL project.
 *
 * Copyright (c) 2024 IMPACTLY OÜ. All rights reserved.
 * This code is proprietary and belongs to IMPACTLY OÜ.
 * Unauthorized copying, distribution, or use of this file is strictly prohibited.
 * Please refer to the LICENSE file in the root directory of this source tree for more details.
 */

"use client"

import type {
  AnimalRow,
  DistrictEnergyRow,
  ElectricityRow,
  FertiliserRow,
  FugitiveRow,
  MobileRow,
  StationaryRow,
} from "@/lib/ghg-calculator/types"
import type { ReactNode } from "react"
import React, { createContext, useContext, useState } from "react"

export type EmissionsSummary = {
  scope1: {
    total: number
    stationary: number
    mobile: number
    fugitive: number
    agricultural: number
    biogenic: number
  }
  scope2: {
    total: number
    electricity: number
    district: number
  }
  resPpaAvailable?: boolean
  biomass?: number
}

// Types for storing complete form data

export type GHGFormData = {
  stationary: {
    fuelRows: StationaryRow[]
    biomassRows: StationaryRow[]
  }
  mobile: MobileRow[]
  fugitive: FugitiveRow[]
  agricultural: {
    animalRows: AnimalRow[]
    fertiliserRows: FertiliserRow[]
  }
  electricity: ElectricityRow[]
  district: DistrictEnergyRow[]
  metadata?: {
    lastSaved?: string
    version?: string
  }
  calculatedTotals?: {
    sectionTotals: Record<string, number>
    scope1Total: number
    scope2Total: number
    scope1AndScope2TotalEmissions: number
  }
}

const defaultFormData: GHGFormData = {
  stationary: {
    fuelRows: [],
    biomassRows: [],
  },
  mobile: [],
  fugitive: [],
  agricultural: {
    animalRows: [],
    fertiliserRows: [],
  },
  electricity: [],
  district: [],
  metadata: {
    version: "1.0",
  },
}

const defaultEmissions: EmissionsSummary = {
  scope1: {
    total: 0,
    stationary: 0,
    mobile: 0,
    fugitive: 0,
    agricultural: 0,
    biogenic: 0,
  },
  scope2: {
    total: 0,
    electricity: 0,
    district: 0,
  },
  resPpaAvailable: false,
  biomass: 0,
}

const EmissionsContext = createContext<{
  emissions: EmissionsSummary
  setEmissions: React.Dispatch<React.SetStateAction<EmissionsSummary>>
  formData: GHGFormData
  setFormData: React.Dispatch<React.SetStateAction<GHGFormData>>
  saveFormData: (year?: number) => Promise<void>
  loadFormData: (data: GHGFormData) => void
  resetFormData: () => void
}>({
  emissions: defaultEmissions,
  setEmissions: () => {
    // Default no-op implementation
  },
  formData: defaultFormData,
  setFormData: () => {
    // Default no-op implementation
  },
  saveFormData: async () => {
    // Default no-op implementation
  },
  loadFormData: () => {
    // Default no-op implementation
  },
  resetFormData: () => {
    // Default no-op implementation
  },
})

export const EmissionsProvider = ({ children }: { children: ReactNode }) => {
  const [emissions, setEmissions] = useState<EmissionsSummary>(defaultEmissions)
  const [formData, setFormData] = useState<GHGFormData>(defaultFormData)

  const saveFormData = async (year?: number) => {
    try {
      const dataToSave = {
        ...formData,
        metadata: {
          ...formData.metadata,
          lastSaved: new Date().toISOString(),
          year: year ?? new Date().getFullYear(),
        },
      }

      // Save to year-specific localStorage key
      const storageKey = year ? `ghg-calculator-data-${year}` : "ghg-calculator-data"
      localStorage.setItem(storageKey, JSON.stringify(dataToSave))

      console.log(`GHG Calculator data saved successfully for ${year ?? "current year"}`)
    } catch (error) {
      console.error("Failed to save GHG Calculator data:", error)
      throw error
    }
  }

  const loadFormData = (data: GHGFormData) => {
    setFormData(data)
  }

  const resetFormData = () => {
    setFormData(defaultFormData)
    setEmissions(defaultEmissions)
  }

  return (
    <EmissionsContext.Provider
      value={{
        emissions,
        setEmissions,
        formData,
        setFormData,
        saveFormData,
        loadFormData,
        resetFormData,
      }}
    >
      {children}
    </EmissionsContext.Provider>
  )
}

export const useEmissions = () => useContext(EmissionsContext)
